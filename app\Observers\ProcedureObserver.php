<?php

namespace App\Observers;

use App\Models\Procedure;
use App\Models\ProcedureVersion;

class ProcedureObserver
{
    /**
     * Handle the Procedure "created" event.
     */
    public function created(Procedure $procedure): void
    {
        // Criar primeira versão
        $this->createVersion($procedure, 'Versão inicial');
    }

    /**
     * Handle the Procedure "updating" event.
     */
    public function updating(Procedure $procedure): void
    {
        // Verificar se houve mudanças significativas
        $significantFields = ['title', 'description', 'legal_basis', 'workflow_steps'];
        $hasSignificantChanges = false;

        foreach ($significantFields as $field) {
            if ($procedure->isDirty($field)) {
                $hasSignificantChanges = true;
                break;
            }
        }

        // Se houve mudanças significativas e não é a primeira criação
        if ($hasSignificantChanges && $procedure->exists) {
            $this->createVersion($procedure, 'Atualização de conteúdo');

            // Incrementar versão
            $procedure->version = $procedure->version + 1;
        }

        // Atualizar campos de auditoria
        $procedure->updated_by = auth()->id();

        // Se está sendo publicado, definir data de publicação
        if ($procedure->isDirty('status') && $procedure->status === 'published') {
            $procedure->published_at = now();
        }
    }

    /**
     * Handle the Procedure "updated" event.
     */
    public function updated(Procedure $procedure): void
    {
        //
    }

    /**
     * Handle the Procedure "deleted" event.
     */
    public function deleted(Procedure $procedure): void
    {
        //
    }

    /**
     * Handle the Procedure "restored" event.
     */
    public function restored(Procedure $procedure): void
    {
        //
    }

    /**
     * Handle the Procedure "force deleted" event.
     */
    public function forceDeleted(Procedure $procedure): void
    {
        //
    }

    /**
     * Criar uma nova versão do procedimento
     */
    private function createVersion(Procedure $procedure, string $changeSummary = null): void
    {
        ProcedureVersion::create([
            'procedure_id' => $procedure->id,
            'version_number' => $procedure->version ?? 1,
            'title' => $procedure->getOriginal('title') ?? $procedure->title,
            'description' => $procedure->getOriginal('description') ?? $procedure->description,
            'legal_basis' => $procedure->getOriginal('legal_basis') ?? $procedure->legal_basis,
            'workflow_steps' => $procedure->getOriginal('workflow_steps') ?? $procedure->workflow_steps,
            'status' => $procedure->getOriginal('status') ?? $procedure->status,
            'created_by' => auth()->id() ?? $procedure->created_by,
            'change_summary' => $changeSummary,
            'created_at' => now(),
        ]);
    }
}
