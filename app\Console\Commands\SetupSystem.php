<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SetupSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system:setup {--force : Forçar a execução mesmo se já configurado}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Configurar o sistema completo (migrações, seeders, cache)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Configurando o Sistema de Procedimentos Internos...');
        $this->newLine();

        // Verificar se já foi configurado
        if (!$this->option('force') && \App\Models\User::count() > 0) {
            if (!$this->confirm('O sistema parece já estar configurado. Deseja continuar?')) {
                $this->info('Configuração cancelada.');
                return 0;
            }
        }

        // Limpar cache
        $this->info('🧹 Limpando cache...');
        $this->call('config:clear');
        $this->call('route:clear');
        $this->call('view:clear');

        // Executar migrações
        $this->info('📊 Executando migrações...');
        $this->call('migrate', ['--force' => true]);

        // Executar seeders
        $this->info('🌱 Executando seeders...');
        $this->call('db:seed', ['--force' => true]);

        // Publicar assets do Filament
        $this->info('📦 Publicando assets...');
        $this->call('filament:upgrade');

        // Criar link de storage (se necessário)
        if (!file_exists(public_path('storage'))) {
            $this->info('🔗 Criando link de storage...');
            $this->call('storage:link');
        }

        $this->newLine();
        $this->info('✅ Sistema configurado com sucesso!');
        $this->newLine();

        // Mostrar informações de acesso
        $this->table(['Informação', 'Valor'], [
            ['URL do Sistema', url('/admin')],
            ['E-mail Admin', '<EMAIL>'],
            ['Senha Admin', 'password'],
            ['Documentação', 'README_SISTEMA.md'],
        ]);

        $this->newLine();
        $this->info('💡 Dicas:');
        $this->line('• Para criar novos usuários: php artisan admin:create-user');
        $this->line('• Para iniciar o servidor: php artisan serve');
        $this->line('• Para ver logs: tail -f storage/logs/laravel.log');

        return 0;
    }
}
