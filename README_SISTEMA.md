# Sistema de Procedimentos Internos

Sistema desenvolvido em Laravel 12 com Filament 3 para gestão de procedimentos internos de cartórios e órgãos públicos.

## 🚀 Funcionalidades Implementadas

### ✅ Autenticação e Autorização
- Sistema de roles com 3 níveis:
  - **Administrador**: Acesso total ao sistema
  - **Escrevente Sênior**: <PERSON><PERSON> criar, editar e publicar procedimentos
  - **Escrevente Júnior**: Pode criar e editar apenas seus próprios procedimentos em rascunho

### ✅ Gestão de Categorias
- CRUD completo de categorias
- Ordenação personalizada
- Status ativo/inativo
- Ícones personalizáveis

### ✅ Gestão de Procedimentos
- Editor WYSIWYG para conteúdo rico
- Sistema de status (Rascunho, Pendente de Revisão, Publicado)
- Versionamento automático
- Upload de anexos (PDF, DOCX, imagens)
- Base legal e fluxo de trabalho detalhado
- Código interno para referência

### ✅ Sistema de Versionamento
- Histórico completo de alterações
- Rollback para versões anteriores
- Log de mudanças automático

### ✅ Log de Atividades
- Auditoria completa de todas as ações
- Rastreamento de usuários
- Filtros por tipo e data

### ✅ Dashboard Administrativo
- Estatísticas em tempo real
- Widgets informativos
- Interface responsiva

## 🛠️ Tecnologias Utilizadas

- **Laravel 12**: Framework PHP
- **Filament 3**: Painel administrativo
- **Spatie Laravel Permission**: Sistema de permissões
- **Spatie Laravel ActivityLog**: Log de atividades
- **Spatie Laravel MediaLibrary**: Gestão de arquivos
- **SQLite**: Banco de dados (configurável)

## 📦 Instalação

### Pré-requisitos
- PHP 8.2+
- Composer
- Node.js e NPM

### Passos de Instalação

1. **Clone o repositório** (se aplicável)
```bash
git clone [url-do-repositorio]
cd intranet
```

2. **Instale as dependências**
```bash
composer install
npm install
```

3. **Configure o ambiente**
```bash
cp .env.example .env
php artisan key:generate
```

4. **Execute as migrações e seeders**
```bash
php artisan migrate
php artisan db:seed
```

5. **Compile os assets**
```bash
npm run build
```

6. **Inicie o servidor**
```bash
php artisan serve
```

## 👤 Usuários Padrão

### Usuário Administrador Criado pelo Seeder
- **E-mail**: <EMAIL>
- **Senha**: password
- **Função**: Administrador

### Usuário Criado Manualmente
- **E-mail**: <EMAIL>
- **Senha**: 123456
- **Função**: Administrador

## 🔧 Comandos Úteis

### Criar Novo Usuário Administrador
```bash
php artisan admin:create-user
```

### Limpar Cache
```bash
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### Executar Seeders
```bash
php artisan db:seed --class=RoleSeeder
php artisan db:seed --class=ProcedureSeeder
```

## 🌐 Acesso ao Sistema

- **URL do Painel**: http://localhost:8000/admin
- **Login**: Use um dos usuários listados acima

## 📁 Estrutura do Projeto

```
app/
├── Filament/Admin/Resources/     # Resources do Filament
├── Models/                       # Models Eloquent
├── Policies/                     # Políticas de autorização
├── Observers/                    # Observers para eventos
└── Console/Commands/             # Comandos artisan personalizados

database/
├── migrations/                   # Migrações do banco
└── seeders/                     # Seeders para dados iniciais
```

## 🔐 Sistema de Permissões

### Administrador
- Acesso total a todas as funcionalidades
- Pode gerenciar usuários e categorias
- Pode publicar qualquer procedimento

### Escrevente Sênior
- Pode criar e editar procedimentos
- Pode revisar e publicar procedimentos
- Não pode gerenciar usuários

### Escrevente Júnior
- Pode criar procedimentos
- Pode editar apenas seus próprios procedimentos em rascunho
- Não pode publicar procedimentos

## 📊 Funcionalidades do Dashboard

- Contadores de procedimentos por status
- Estatísticas de categorias ativas
- Número de usuários cadastrados
- Gráficos e métricas em tempo real

## 🔄 Sistema de Versionamento

Toda alteração significativa em um procedimento gera automaticamente:
- Nova versão numerada
- Backup da versão anterior
- Log da alteração com usuário e timestamp
- Possibilidade de rollback

## 📎 Upload de Arquivos

Suporte para anexos:
- **Formatos**: PDF, DOCX, XLSX, JPEG, PNG
- **Tamanho máximo**: 10MB por arquivo
- **Múltiplos arquivos**: Sim
- **Visualização**: Integrada no painel

## 🎨 Interface

- Design responsivo (desktop, tablet, mobile)
- Tema personalizado com cores institucionais
- Navegação organizada por grupos
- Busca global integrada
- Editor WYSIWYG para conteúdo rico

## 🐛 Solução de Problemas

### Erro de Permissão
Se encontrar erros de permissão, execute:
```bash
php artisan config:clear
php artisan route:clear
```

### Problemas com Filament
Se o painel não carregar:
```bash
php artisan filament:upgrade
```

### Recriar Usuário Administrador
```bash
php artisan tinker
$user = \App\Models\User::create(['name' => 'Admin', 'email' => '<EMAIL>', 'password' => bcrypt('password')]);
$user->assignRole('Administrador');
```

## 📞 Suporte

Para dúvidas ou problemas:
1. Verifique os logs em `storage/logs/laravel.log`
2. Execute os comandos de limpeza de cache
3. Consulte a documentação do Laravel e Filament

---

**Sistema desenvolvido com Laravel 12 + Filament 3**
