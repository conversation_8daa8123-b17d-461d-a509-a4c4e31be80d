<?php

namespace App\Filament\Admin\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Procedure;
use App\Models\Category;
use App\Models\User;

class ProceduresStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total de Procedimentos', Procedure::count())
                ->description('Todos os procedimentos cadastrados')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),

            Stat::make('Procedimentos Publicados', Procedure::where('status', 'published')->count())
                ->description('Procedimentos ativos e disponíveis')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Pendentes de Revisão', Procedure::where('status', 'pending_review')->count())
                ->description('Aguardando aprovação')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Rascunhos', Procedure::where('status', 'draft')->count())
                ->description('Em desenvolvimento')
                ->descriptionIcon('heroicon-m-pencil-square')
                ->color('gray'),

            Stat::make('Categorias Ativas', Category::where('is_active', true)->count())
                ->description('Categorias disponíveis')
                ->descriptionIcon('heroicon-m-folder')
                ->color('info'),

            Stat::make('Usuários Ativos', User::count())
                ->description('Total de usuários cadastrados')
                ->descriptionIcon('heroicon-m-users')
                ->color('secondary'),
        ];
    }
}
