<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use App\Models\Category;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Criar permissões
        $permissions = [
            'view_procedures',
            'create_procedures',
            'edit_procedures',
            'delete_procedures',
            'publish_procedures',
            'review_procedures',
            'manage_categories',
            'manage_users',
            'view_activity_log',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Criar roles
        $adminRole = Role::firstOrCreate(['name' => 'Administrador']);
        $seniorRole = Role::firstOrCreate(['name' => 'Escrevente Sênior']);
        $juniorRole = Role::firstOrCreate(['name' => 'Escrevente Júnior']);

        // Atribuir permissões aos roles
        $adminRole->givePermissionTo($permissions);

        $seniorRole->givePermissionTo([
            'view_procedures',
            'create_procedures',
            'edit_procedures',
            'delete_procedures',
            'publish_procedures',
            'review_procedures',
        ]);

        $juniorRole->givePermissionTo([
            'view_procedures',
            'create_procedures',
            'edit_procedures',
        ]);

        // Criar usuário administrador
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrador',
                'password' => bcrypt('password'),
            ]
        );
        $admin->assignRole('Administrador');

        // Criar categorias de exemplo
        $categories = [
            ['name' => 'Alienação Fiduciária', 'description' => 'Procedimentos relacionados à alienação fiduciária', 'sort_order' => 1],
            ['name' => 'Hipoteca', 'description' => 'Procedimentos relacionados à hipoteca', 'sort_order' => 2],
            ['name' => 'ITBI', 'description' => 'Procedimentos relacionados ao ITBI', 'sort_order' => 3],
            ['name' => 'Subdivisão', 'description' => 'Procedimentos relacionados à subdivisão', 'sort_order' => 4],
            ['name' => 'Registro de Imóveis', 'description' => 'Procedimentos gerais de registro de imóveis', 'sort_order' => 5],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }
    }
}
