<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckFilamentPermissions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Verificar se o usuário tem pelo menos uma role
        if (auth()->check() && !auth()->user()->hasAnyRole(['Administrador', 'Escrevente Sênior', 'Escrevente Júnior'])) {
            abort(403, '<PERSON>sso negado. Você não tem permissão para acessar o painel administrativo.');
        }

        return $next($request);
    }
}
