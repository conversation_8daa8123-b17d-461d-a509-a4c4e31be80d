<?php

namespace App\Policies;

use App\Models\Procedure;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProcedurePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasAnyRole(['Administrador', 'Escrevente Sênior', 'Escrevente Júnior']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Procedure $procedure): bool
    {
        return $user->hasAnyRole(['Administrador', 'Escrevente Sênior', 'Escrevente Júnior']);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasAnyRole(['Administrador', 'Escrevente Sênior', 'Escrevente Júnior']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Procedure $procedure): bool
    {
        // Administrador pode editar qualquer procedimento
        if ($user->hasRole('Administrador')) {
            return true;
        }

        // Escrevente Sênior pode editar qualquer procedimento
        if ($user->hasRole('Escrevente Sênior')) {
            return true;
        }

        // Escrevente Júnior só pode editar seus próprios procedimentos em rascunho
        if ($user->hasRole('Escrevente Júnior')) {
            return $procedure->created_by === $user->id && $procedure->status === 'draft';
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Procedure $procedure): bool
    {
        // Administrador pode deletar qualquer procedimento
        if ($user->hasRole('Administrador')) {
            return true;
        }

        // Escrevente Sênior pode deletar procedimentos não publicados
        if ($user->hasRole('Escrevente Sênior')) {
            return $procedure->status !== 'published';
        }

        // Escrevente Júnior só pode deletar seus próprios rascunhos
        if ($user->hasRole('Escrevente Júnior')) {
            return $procedure->created_by === $user->id && $procedure->status === 'draft';
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Procedure $procedure): bool
    {
        return $user->hasRole('Administrador');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Procedure $procedure): bool
    {
        return $user->hasRole('Administrador');
    }
}
