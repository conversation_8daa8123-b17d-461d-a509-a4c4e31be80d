<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create-user {--name=} {--email=} {--password=} {--role=Administrador}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Criar um usuário administrador para o sistema';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Criação de Usuário Administrador ===');

        // Obter dados do usuário
        $name = $this->option('name') ?: $this->ask('Nome do usuário');
        $email = $this->option('email') ?: $this->ask('E-mail do usuário');
        $password = $this->option('password') ?: $this->secret('Senha do usuário');
        $role = $this->option('role');

        // Validar dados
        $validator = Validator::make([
            'name' => $name,
            'email' => $email,
            'password' => $password,
        ], [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            $this->error('Dados inválidos:');
            foreach ($validator->errors()->all() as $error) {
                $this->error('- ' . $error);
            }
            return 1;
        }

        // Criar usuário
        try {
            $user = User::create([
                'name' => $name,
                'email' => $email,
                'password' => Hash::make($password),
                'email_verified_at' => now(),
            ]);

            // Atribuir role
            $user->assignRole($role);

            $this->info("✅ Usuário criado com sucesso!");
            $this->table(['Campo', 'Valor'], [
                ['Nome', $user->name],
                ['E-mail', $user->email],
                ['Função', $role],
                ['ID', $user->id],
            ]);

            $this->info("🔗 Acesse o painel em: " . url('/admin'));

            return 0;

        } catch (\Exception $e) {
            $this->error('Erro ao criar usuário: ' . $e->getMessage());
            return 1;
        }
    }
}
