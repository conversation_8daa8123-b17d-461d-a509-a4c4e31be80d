<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Procedure;
use App\Models\Category;
use App\Models\User;

class ProcedureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('email', '<EMAIL>')->first();
        if (!$admin) {
            $admin = User::first();
        }

        if (!$admin) {
            $this->command->warn('Nenhum usuário encontrado. Execute o RoleSeeder primeiro.');
            return;
        }

        $categories = Category::all();

        if ($categories->isEmpty()) {
            $this->command->warn('Nenhuma categoria encontrada. Execute o RoleSeeder primeiro.');
            return;
        }

        $procedures = [
            [
                'category' => 'Alienação Fiduciária',
                'title' => 'Procedimento para Registro de Alienação Fiduciária',
                'code' => 'AF-001',
                'description' => '<h2>Procedimento para Registro de Alienação Fiduciária</h2><p>Este procedimento descreve os passos necessários para o registro de alienação fiduciária em garantia.</p><h3>Documentos Necessários:</h3><ul><li>Contrato de alienação fiduciária</li><li>Certidões negativas</li><li>Documentos pessoais das partes</li></ul>',
                'legal_basis' => 'Lei nº 9.514/97 - Sistema de Financiamento Imobiliário',
                'workflow_steps' => '<h3>Fluxo de Trabalho:</h3><ol><li><strong>Recebimento da documentação</strong><br>Verificar se todos os documentos estão presentes e válidos</li><li><strong>Análise jurídica</strong><br>Examinar a legalidade do contrato e das cláusulas</li><li><strong>Cálculo de emolumentos</strong><br>Calcular as taxas devidas conforme tabela vigente</li><li><strong>Registro</strong><br>Efetuar o registro no livro próprio</li><li><strong>Entrega</strong><br>Entregar a certidão de registro às partes</li></ol>',
                'status' => 'published',
            ],
            [
                'category' => 'ITBI',
                'title' => 'Procedimento para Cálculo e Recolhimento do ITBI',
                'code' => 'ITBI-001',
                'description' => '<h2>Procedimento para Cálculo e Recolhimento do ITBI</h2><p>Este procedimento estabelece as regras para cálculo e recolhimento do Imposto sobre Transmissão de Bens Imóveis.</p>',
                'legal_basis' => 'Código Tributário Municipal - Lei Municipal específica de cada município',
                'workflow_steps' => '<h3>Passos para Cálculo:</h3><ol><li>Verificar o valor venal do imóvel</li><li>Aplicar a alíquota municipal</li><li>Verificar isenções aplicáveis</li><li>Emitir guia de recolhimento</li></ol>',
                'status' => 'published',
            ],
            [
                'category' => 'Hipoteca',
                'title' => 'Registro de Hipoteca Convencional',
                'code' => 'HIP-001',
                'description' => '<h2>Registro de Hipoteca Convencional</h2><p>Procedimento para registro de hipoteca convencional como garantia real.</p>',
                'legal_basis' => 'Código Civil Brasileiro - Arts. 1.473 a 1.505',
                'workflow_steps' => '<h3>Etapas do Registro:</h3><ol><li>Análise da escritura de hipoteca</li><li>Verificação da propriedade do bem</li><li>Cálculo dos emolumentos</li><li>Registro no livro 2</li></ol>',
                'status' => 'draft',
            ],
        ];

        foreach ($procedures as $procedureData) {
            $category = $categories->where('name', $procedureData['category'])->first();

            if ($category) {
                Procedure::create([
                    'category_id' => $category->id,
                    'title' => $procedureData['title'],
                    'code' => $procedureData['code'],
                    'description' => $procedureData['description'],
                    'legal_basis' => $procedureData['legal_basis'],
                    'workflow_steps' => $procedureData['workflow_steps'],
                    'status' => $procedureData['status'],
                    'created_by' => $admin->id,
                    'updated_by' => $admin->id,
                    'published_at' => $procedureData['status'] === 'published' ? now() : null,
                    'version' => 1,
                ]);
            }
        }
    }
}
