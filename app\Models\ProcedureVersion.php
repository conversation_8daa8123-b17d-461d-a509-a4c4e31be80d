<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProcedureVersion extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $fillable = [
        'procedure_id',
        'version_number',
        'title',
        'description',
        'legal_basis',
        'workflow_steps',
        'status',
        'created_by',
        'change_summary',
        'created_at',
    ];

    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
        ];
    }

    public function procedure()
    {
        return $this->belongsTo(Procedure::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
