# Requisitos Funcionais

## 1. Autenticação e Autorização

* **Cadastro de usuários com papéis (roles) diferentes**, por exemplo:
    * **Administrador (Registrador e TI):** pode criar e editar todos os procedimentos, categorias e usuários.
    * **Escrevente Sênior:** pode criar/editar procedimentos, revisar conteúdo e aprovar publicações.
    * **Escrevente Júnior:** pode visualizar e sugerir alterações nos procedimentos, mas não publicar.
* **Controle de acesso baseado em papéis**, garantindo que cada função só veja/edite o que lhe cabe.

---

## 2. Gestão de Categorias e Procedimentos

* **CRUD de Categorias:** ex.: “Alienação Fiduciária”, “Hipoteca”, “ITBI”, “Subdivisão” etc.
    * Cada categoria possui: nome, descrição breve, ícone (opcional) e ordem de exibição.
* **CRUD de Procedimentos/Normas dentro de cada categoria:**
    * **Campos principais de um procedimento:**
        * Título (ex.: “Procedimento para ITBI”)
        * Código interno (opcional, para referência)
        * Descrição detalhada (editor WYSIWYG para texto rico)
        * Base legal (link para leis, artigos, portarias)
        * Fluxo de trabalho passo a passo (textos, checklists, tabelas)
        * Data de criação e data de última atualização (preenchidos automaticamente)
        * Usuário responsável pela criação e pelo último update
        * Anexos de arquivos (PDF, DOCX, imagens) relacionados
        * Status (rascunho, pendente de revisão, publicado)
        * Histórico de versões: sempre que um procedimento for alterado, armazenar versão anterior para possibilitar auditoria e rollback.
        * Botão para “Enviar para revisão” (se o usuário for Júnior) ou “Publicar” (se for Sênior ou Administrador).

---

## 3. Pesquisa e Navegação

* **Barra de busca global** (Título + Conteúdo + Base Legal).
* **Filtro por categoria e por status** (only publicados, only pendentes, etc.).
* **Paginação ou scroll infinito** para listas longas de procedimentos.
* **Navegação em “breadcrumbs”** para facilitar saber em qual categoria/nível o usuário está.

---

## 4. Notificações e Alertas

* **Painel “Dashboard”** para o Administrador exibir pendências (procedimentos aguardando revisão, documentos expirados etc.).

---

## 5. Área Pública x Área Interna

* O acesso ao conteúdo deve ser **restrito ao ambiente interno**, mas considere criar uma área de consulta pública (opcional) na qual qualquer funcionário logado possa visualizar apenas os procedimentos “Publicados”.
* Para procedimentos em “Rascunho” ou “Pendente de Revisão”, somente usuários com permissão adequada (perfil Sênior/Admin) conseguem ver ou editar.

---

## 6. Versionamento e Auditoria

* **Registrar em log todas as alterações** feitas nos procedimentos:
    * Quem alterou, quando, e resumo da mudança.
* Permitir **“rollback” para uma versão anterior** diretamente pela interface de administração.

---

## 7. Anexos e Documentos

* Permitir **upload de arquivos** (PDF, DOCX, XLSX e imagens).
* **Exibição de lista de anexos** em cada procedimento, com opção de download direto.
* **Controlar tamanho máximo** (ex.: 10 MB por arquivo) e tipos permitidos.

---

## 8. Responsividade e Design

* **Layout responsivo:** deve se adaptar bem a tablets e celulares (Media Queries + Tailwind CSS).
* **Interface de leitura agradável:** boa tipografia, títulos destacados, tabelas e listas claras.