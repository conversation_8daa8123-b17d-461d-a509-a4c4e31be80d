<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Models\Category;
use App\Models\Procedure;
use App\Models\User;
use App\Policies\CategoryPolicy;
use App\Policies\ProcedurePolicy;
use App\Policies\UserPolicy;
use App\Observers\ProcedureObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Gate::policy(Category::class, CategoryPolicy::class);
        Gate::policy(Procedure::class, ProcedurePolicy::class);
        Gate::policy(User::class, UserPolicy::class);

        Procedure::observe(ProcedureObserver::class);
    }
}
