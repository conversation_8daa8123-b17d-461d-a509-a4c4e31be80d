<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('procedure_versions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('procedure_id')->constrained()->onDelete('cascade');
            $table->integer('version_number');
            $table->string('title');
            $table->longText('description');
            $table->text('legal_basis')->nullable();
            $table->longText('workflow_steps')->nullable();
            $table->enum('status', ['draft', 'pending_review', 'published'])->default('draft');
            $table->foreignId('created_by')->constrained('users');
            $table->text('change_summary')->nullable();
            $table->timestamp('created_at');

            $table->unique(['procedure_id', 'version_number']);
            $table->index(['procedure_id', 'version_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('procedure_versions');
    }
};
